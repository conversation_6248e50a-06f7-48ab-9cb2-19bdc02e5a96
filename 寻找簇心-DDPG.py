#2024年9月3日14:08:54
#采用DDPG方法寻找簇心。只有一个簇心，也就是无人机的位置

#通过使得平均吞吐量最大化获取簇心，还采用了蚂蚁算法pso进行验证，完美运行。
#2024年9月4日16:24:11

import sys
sys.path.append('/home/<USER>/external-libraries')
from cmath import inf
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import random
from collections import deque
import os
import matplotlib.pyplot as plt
import datetime
from tqdm import tqdm
from sklearn.preprocessing import MinMaxScaler
import shutil

# ==================================================================================================
class ClusteringEnv:
    """
    2024年9月3日10:27:27
    通过 kmeans 算法确定初始簇划分后，需要重新确定簇中心位置，即无人机的位置。该位置是根据最大化用户平均吞吐量的原则进行调整和优化。

    ==================================================================
    聚类环境类，用于模拟聚类任务的强化学习环境。

    参数:
    data (np.ndarray): 数据集，形状为 [N, 2]，其中 N 是数据点的数量。
    start_point (np.ndarray): 初始质心位置。
    max_action (np.ndarray): 动作的最大幅度。
    min_action (np.ndarray): 动作的最小幅度。
    step_space (float): 动作步长。
    path (str): 数据保存路径。
    """
    def __init__(self, 
                 data: np.ndarray,  # 数据集，形状为 [N, 2]，其中 N 是数据点的数量
                 users_transmit_power: np.ndarray,  # 用户的发射功率
                 start_point: np.ndarray,  # 初始质心位置
                 max_action: np.ndarray,  # 动作的最大幅度
                 min_action: np.ndarray,  # 动作的最小幅度
                 step_space: float,  # 动作步长
                 path: str  # 数据保存路径
                 ):
        self.data = data #地面用户坐标
        self.users_number=len(self.data)
        self.start_point = np.array(start_point, dtype=np.float64)#智能体初始位置，也就是无人机的初始位置        
        self.centroids = np.array(start_point, dtype=np.float64)  # 无人机的当前位置
        self.mean_data=np.mean(self.data, axis=0) #目标位置    
        self.max_action = np.array(100, dtype=np.float64)#边界
        self.min_action = np.array(0,dtype=np.float64)#边界

        self.path=path
        self.step_space=step_space
        self.all_centroids = [self.centroids.copy()]
        self.visited_stations = np.zeros(len(self.data))        
        self.state_dim = self.get_state().size
        self.action_dim = 2

        self.best_reward=False#接近目标点的标志
        self.scaler = MinMaxScaler(feature_range=(0, 1))
        self.users_position_rand=True
        self.users_transmit_power=users_transmit_power
        self.throughput=[]#记录吞吐量

    def get_state(self) -> np.ndarray:
        """
        获取当前状态:当前无人机的位置、地面用户位置

        返回:
        np.ndarray: 当前状态。
        """
        current_position = self.centroids
        users_position = self.data        

        return np.concatenate([current_position.flatten(), users_position.flatten()]).flatten()

    def reset(self) -> np.ndarray:
        """
        重置环境，返回初始状态。
        返回:
        np.ndarray: 初始状态。
        """        
        if self.users_position_rand:
            np.random.seed()
            self.data = (np.random.rand(self.users_number, 2) *100).astype(np.float64)
            # self.users_position_rand=False

        self.centroids = self.start_point.copy()  # 随机初始位置
        self.all_centroids=[self.centroids.copy()]
        self.throughput=[]
        self.mean_data=np.mean(self.data, axis=0) #目标位置 
        

        return self.get_state()

    def get_throughput(self,uav_position):
        #计算吞吐量
        UAV_network=uavNetwork(user_coordinates=self.data,uav_coordinates=uav_position,users_transmit_power=self.users_transmit_power,channels_per_uav=self.users_number)
        average_throughput=np.mean(UAV_network.calculate_capacity_without_interference())

        return average_throughput

    def step(self, 
             actions: np.ndarray,  # 动作
             t: int  # 当前时间步
             ):

        last_position = self.centroids.copy()  # 保存上一次的位置

        self.centroids +=  (actions * self.step_space).squeeze()
        new_state = self.centroids

        reward, done = self.calculate_reward(last_position, t)
        if not done or self.best_reward:
            self.all_centroids.append(new_state.copy())
        else:
            self.throughput=self.throughput[:-1]

        return self.get_state(), reward, done

    def calculate_reward(self, 
                        last_state: np.ndarray,  # 上一次的状态
                        t: int  # 当前时间步
                        ) :
        """
        方法一：直接将吞吐量作为奖励
        方法二：根据吞吐量的增减变化作为奖励

        参数:
        last_state (np.ndarray): 上一次的状态，形状为 [2]。
        t (int): 当前时间步。
        返回:
        Tuple[float, bool]: 奖励和是否完成。
        """
        if any(self.centroids.flatten() < self.min_action) or any(self.centroids.flatten() > self.max_action):
            reward = -100  # 惩罚超出范围的动作
            done = True
            return reward, done  # 惩罚超出范围的动作，并结束回合        
         
        reward = 0
        done = False     
        #计算吞吐量
        last_average_throughput=self.get_throughput(last_state)
        average_throughput=self.get_throughput(self.centroids)
        self.throughput.append(average_throughput)
        
        # 软化吞吐量增长要求
        if average_throughput < last_average_throughput:
            reward -= 10  # 给予较小的惩罚，但不立即结束
            done=True
            return reward, done  # 结束回合
            
        # 归一化吞吐量增益
        throughput_gain = (average_throughput - last_average_throughput) / last_average_throughput
        reward += throughput_gain * 1000  # 放大奖励

        # 路径长度惩罚（权重较小）
        path_length = np.linalg.norm(self.centroids - last_state)
        reward -= path_length * 0.1

        return reward, done

    def pso_throughput(self):#采用蚂蚁算法计算最优位置和吞吐量
        self.pso_uav_position,self.pos_throughput=optimize_drone_position(self.data,self.users_transmit_power,10)
        return self.pso_uav_position,self.pos_throughput

    def render(self,epoch=0):
        """
        渲染当前环境状态，保存图像到指定路径。

        参数:
        epoch (int): 当前的 epoch 数。
        """
        all_centroids=np.array(self.all_centroids)
        plt.plot(all_centroids[:,0],all_centroids[:,1],'r--', linewidth=1)
        plt.plot(self.start_point[0], self.start_point[1], 'go', markersize=10, label='Start')
        plt.plot(self.mean_data[0], self.mean_data[1], 'ro', markersize=10, label='End')
        plt.scatter(self.data[:,0],self.data[:,1],c='g',marker='^',s=50)
        plt.title(f'epoch:{len(all_centroids)}')
        plt.legend()
        plt.xlim(0,100)
        plt.ylim(0,100)
        plt.grid()
        plt.savefig(self.path+f'{epoch}_path.png')
        plt.close()

    def show_picture(self,episode):
        """
        显示当前环境状态的图像。
        """
        all_centroids=np.array(self.all_centroids)
        pso_position,pso_throughput=self.pso_throughput()
        plt.figure()
        plt.plot(all_centroids[:,0],all_centroids[:,1],'r--', linewidth=1)
        plt.plot(self.start_point[0], self.start_point[1], 'go', markersize=10, label='Start')
        plt.plot(self.mean_data[0], self.mean_data[1], 'ro', markersize=10, label='mean')
        plt.plot(pso_position[0], pso_position[1], 'bp', markersize=10, label='pso')
        plt.scatter(self.data[:,0],self.data[:,1],c='g',marker='^',s=50)
        plt.title(f'epoch:{len(all_centroids)}')
        plt.legend()
        plt.xlim(0,100)
        plt.ylim(0,100)
        plt.grid()
        plt.savefig(self.path+f'{episode}_eval.png')
        
        plt.figure()
        # 找出最大值及其索引
        max_throughput = np.max(self.throughput)
        max_index = np.argmax(self.throughput)
        # 绘制最大值点
        plt.plot(max_index, max_throughput, 'bo', markersize=10)

        plt.plot(self.throughput,'r.-', linewidth=1)
        plt.axhline(y=max_throughput, color='g', linestyle='--', linewidth=1)

        plt.plot(pso_throughput, 'bo-', linewidth=1)
        plt.savefig(self.path+f'{episode}_throughput.png')
        

    def save_data(self,epoch):
        """
        保存当前的质心数据到指定路径。

        参数:
        epoch (int): 当前的 epoch 数。
        """
        np.savetxt(self.path+f'{epoch}_data.txt',self.all_centroids)

# ==================================================================================================
    #粒子群算法
def optimize_drone_position(gt_axis,gt_power,max_iter):
    
    # 目标函数
    def objective(U):
        xu, yu = U
        mean_throghput = 0
        drone_network = uavNetwork(user_coordinates=gt_axis,uav_coordinates=[xu, yu, 100],users_transmit_power=gt_power,channels_per_uav=len(gt_power))
        mean_throghput = np.mean(drone_network.calculate_capacity_without_interference())
        return -mean_throghput # Minimize negative sum rate for maximization problem

    # 粒子群优化约束条件
    lb = [0, 0] # 下界
    ub = [100, 100] # 上界

    # 粒子群优化
    max_iter = max_iter  # 设置迭代次数为100
    xopt, fopt = pso(objective, lb, ub, maxiter=max_iter,swarmsize=30)

    print("Optimal Location:", xopt)
    print("Maximum Sum Rate:", -fopt)
    # pass
    return xopt, -fopt  # 返回最优位置和最大总速率（注意取负号）   

# ==================================================================================================
class uavNetwork:
    def __init__(
        self,
        user_coordinates=None,
        uav_coordinates=None,
        labels=None,
        subchannels=None,
        users_transmit_power=None,
        carrier_frequency=2e9,
        noise_power=-170,
        channels_per_uav=25,
    ):
        self.user_coordinates = np.array(user_coordinates)
        self.uav_coordinates = np.array(uav_coordinates)
        self.check_coordinates()
        self.labels = labels
        self.subchannels = subchannels
        self.users_transmit_power = users_transmit_power  # 单位dB
        self.carrier_frequency = carrier_frequency
        self.noise_power = noise_power  # 单位dBm
        self.channels_per_uav = channels_per_uav

    #检查user_coordinates和uav_coordinates的维度，如果都是2列，则补全成3列，采用补0方式
    def check_coordinates(self):
        if self.user_coordinates.shape[1] == 2:
            self.user_coordinates = np.hstack(
                (self.user_coordinates, np.zeros((self.user_coordinates.shape[0], 1)))
            )
        if self.uav_coordinates.size == 2:
            self.uav_coordinates=np.append(self.uav_coordinates,100)

    # 计算用户与无人机之间的距离（欧氏距离）
    def calculate_distance(self, user_coords, uav_coords):
        # distance = np.sqrt(
        #     (user_coords[0] - uav_coords[0]) ** 2
        #     + (user_coords[1] - uav_coords[1]) ** 2
        #     + (user_coords[2] - uav_coords[2]) ** 2
        # )
        distance=np.linalg.norm(user_coords-uav_coords)
        return distance

    def calculate_elevation_angle(self, uav_coords, user_coords):
        # 计算仰角（弧度）
        phi_rad = np.arctan2(
            uav_coords[2] - user_coords[2],
            np.sqrt((uav_coords[0] - user_coords[0]) ** 2 + (uav_coords[1] - user_coords[1]) ** 2),
        )
        # phi_rad = np.arctan2(
        #     100,
        #     np.sqrt((uav_coords[0] - user_coords[0]) ** 2 + (uav_coords[1] - user_coords[1]) ** 2),
        # )

        # 转换为角度
        phi_deg = np.degrees(phi_rad)
        return phi_deg

    # 计算路径损耗（单位：dB）
    def calculate_path_loss(self, user_coords, uav_coords):
        a = 9.61  # 单位：无
        b = 0.16  # 单位：无
        # eta_Los, eta_NLos=1,21 乡村
        eta_Los, eta_NLos = 1, 20  # 单位：dB，可以理解为路径损耗中的衰减因子
        # eta_Los, eta_NLos=1.6,23 #密集城市
        # eta_Los, eta_NLos=2.3,34 #高楼城市
        C = 3e8  # 光速，单位：米/秒

        distance = self.calculate_distance(user_coords, uav_coords)  # 单位：米
        theta = self.calculate_elevation_angle(uav_coords, user_coords)  # 单位：度
        # the probable of the pass loss，概率因子，无单位
        prob_loss = 1 / (1 + a * np.exp(-b * (theta - a)))
        # Los，直射路径损耗，单位：dB
        Los = 20 * np.log10(4 * np.pi * self.carrier_frequency * distance / C) + eta_Los
        # NLos，非直射路径损耗，单位：dB
        NLos = Los - eta_Los + eta_NLos
        # path_loss，总路径损耗，单位：dB
        path_loss = prob_loss * Los + (1 - prob_loss) * NLos

        return path_loss

    # 计算干扰功率
    def calculate_interference_power(self):
        interference_power = np.zeros(len(self.labels))  # 初始化每个用户的干扰噪声

        for i in range(len(self.labels)):  # 遍历所有用户
            current_uav = self.labels[i]  # 当前用户所关联的无人机
            current_subchannel = self.subchannels[i]  # 当前用户分配的信道
            current_interference = 0  # 初始化当前用户的干扰噪声
            # 所有相同信道的索引
            indices_same_subchannel = np.where(np.array(self.subchannels) == current_subchannel)[0]

            for j in indices_same_subchannel:  # 遍历索引
                if j != i:  # 排除当前用户
                    path_loss = self.calculate_path_loss(
                        self.user_coordinates[j], self.uav_coordinates[current_uav]
                    )  # 计算路径损耗，单位dB
                    current_interference += 10 ** ((self.users_transmit_power[j] - path_loss) / 10)  # 单位w

            interference_power[i] = current_interference  # 单位w

        return interference_power

    # 计算用户的信号功率(不包含干扰功率)
    def calculate_users_power(self):
        if self.uav_coordinates.size>3:
            users_power = np.zeros(len(self.user_coordinates))
            for j in range(len(self.labels)):
                current_uav = self.labels[j]
                path_loss = self.calculate_path_loss(self.user_coordinates[j], self.uav_coordinates[current_uav])
                users_power[j] = 10 ** ((self.users_transmit_power[j] - path_loss) / 10)
        else:
            users_power = np.zeros(len(self.user_coordinates))
            for j in range(len(users_power)):
                path_loss = self.calculate_path_loss(self.user_coordinates[j], self.uav_coordinates)
                users_power[j] = 10 ** ((self.users_transmit_power[j] - path_loss) / 10)
        return users_power

    # 计算用户的信道容量（考虑同频干扰和噪声）
    def calculate_interference_capacity(self):
        sub_bandwidth = 2e6 / self.channels_per_uav
        interference_power = self.calculate_interference_power()
        users_power = self.calculate_users_power()
        noise_power_density = 10 ** (self.noise_power / 10) / 1000  # 单位w
        users_capacity = []

        for interference, user_power in zip(interference_power, users_power):
            capacity = sub_bandwidth * np.log2(1 + user_power / (interference + noise_power_density))
            users_capacity.append(capacity)

        return np.array(users_capacity)

    # 计算用户的信道容量（不考虑同频干扰和噪声）
    def calculate_capacity_without_interference(self):
        sub_bandwidth = 2e6 / self.channels_per_uav
        noise_power_density = 10 ** (self.noise_power / 10) / 1000  # 单位w
        users_power = self.calculate_users_power()
        users_capacity = sub_bandwidth * np.log2(1 + users_power / noise_power_density)
        return users_capacity

    # 计算每个簇内用户的平均吞吐量
    def calculate_average_throughput(self):
        users_capacity = self.calculate_interference_capacity()  # 每个用户的信道容量
        num_clusters = len(self.uav_coordinates)  # 分簇数量
        average_throughput = np.zeros(num_clusters)
        for i in range(num_clusters):
            cluster_throughputs = users_capacity[self.labels == i]
            average_throughput[i] = np.mean(cluster_throughputs)

        return average_throughput

# ==================================================================================================



# 定义Actor网络，输入为状态，输出为动作
class Actor(nn.Module):
    def __init__(self, state_dim, action_dim, max_action):
        super(Actor, self).__init__()
        self.fc1 = nn.Linear(state_dim, 400)
        self.fcn1 = nn.LayerNorm(400)  # 使用LayerNorm替换BatchNorm1d
        self.fc2 = nn.Linear(400, 300)
        self.fcn2 = nn.LayerNorm(300)  # 使用LayerNorm替换BatchNorm1d
        self.fc3 = nn.Linear(300, action_dim)
        self.dropout = nn.Dropout(0.1)  # 添加Dropout层
        
    def forward(self, state):
        x = self.fc1(state)
        # x = self.fcn1(x)
        x = torch.relu(x)        
        x = self.fc2(x)
        # x = self.fcn2(x)
        x = torch.relu(x)        
        x = self.dropout(x)  # 应用Dropout        
        x = self.fc3(x)
        x = nn.functional.normalize(x, p=2, dim=-1)  # 这里使用L2范数归一化
        # print(f'===================x:{x}')
        x = torch.tanh(x)
        
        return x

# 定义Critic网络，输入为状态和动作，输出为Q值
class Critic(nn.Module):
    def __init__(self, state_dim, action_dim):
        super(Critic, self).__init__()
        self.l1 = nn.Linear(state_dim + action_dim, 512)
        self.l2 = nn.Linear(512, 256)
        self.l3 = nn.Linear(256, 1)

        # 权重初始化
        self._initialize_weights()

    def _initialize_weights(self):
        nn.init.xavier_uniform_(self.l1.weight)
        nn.init.xavier_uniform_(self.l2.weight)
        nn.init.xavier_uniform_(self.l3.weight)

    def forward(self, x, u):
        x = torch.relu(self.l1(torch.cat([x, u], 1)))
        x = torch.relu(self.l2(x))
        x = self.l3(x)
        # print(f'===================x:{x}') 
        # x = nn.functional.normalize(x, p=2, dim=-1)  # 这里使用L2范数归一化
        return x

# 定义经验回放池
class ReplayBuffer:
    def __init__(self, max_size=10000):
        self.buffer = deque(maxlen=max_size)

    def add(self, state, action, reward, next_state, done):
        self.buffer.append((state, action, reward, next_state, done))

    def sample(self, batch_size):
        state, action, reward, next_state, done = zip(*random.sample(self.buffer, batch_size))
        return (np.array(state), np.array(action), np.array(reward), np.array(next_state), np.array(done))

    def size(self):
        return len(self.buffer)

# DDPG算法主体
class DDPG:
    def __init__(self, state_dim, action_dim, max_action, gamma=0.99, tau=0.005):
        self.actor = Actor(state_dim, action_dim, max_action).to(device)
        self.actor_target = Actor(state_dim, action_dim, max_action).to(device)
        self.actor_target.load_state_dict(self.actor.state_dict())
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=0.001)

        self.critic = Critic(state_dim, action_dim).to(device)
        self.critic_target = Critic(state_dim, action_dim).to(device)
        self.critic_target.load_state_dict(self.critic.state_dict())
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=0.001)
        self.gamma = gamma
        self.tau = tau
        self.train_mode=True

    def select_action(self, state):
        state = torch.FloatTensor(state.reshape(1, -1)).to(device)
        return self.actor(state).cpu().data.numpy().flatten()

    def train(self, replay_buffer, batch_size=64):
        state, action, reward, next_state, done = replay_buffer.sample(batch_size)

        state = torch.FloatTensor(state).to(device)
        action = torch.FloatTensor(action).to(device).squeeze()
        reward = torch.FloatTensor(reward).to(device).view(-1,1)
        next_state = torch.FloatTensor(next_state).to(device)
        done = torch.FloatTensor(done).to(device).view(-1,1)

        # 更新Critic网络
        target_Q = self.critic_target(next_state, self.actor_target(next_state))
        # print(next_state.shape, self.actor_target(next_state).shape, target_Q.shape,reward.shape,done.shape)
        target_Q = reward + ((1 - done) * self.gamma * target_Q).detach()
        current_Q = self.critic(state, action)
        critic_loss = nn.MSELoss()(current_Q, target_Q)
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()

        # 更新Actor网络
        actor_loss = -self.critic(state, self.actor(state)).mean()
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        self.actor_optimizer.step()

        # 更新目标网络
        for param, target_param in zip(self.critic.parameters(), self.critic_target.parameters()):
            target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

        for param, target_param in zip(self.actor.parameters(), self.actor_target.parameters()):
            target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

    def load(self, actor_path, critic_path):
        self.actor.load_state_dict(torch.load(actor_path, map_location=device))
        self.critic.load_state_dict(torch.load(critic_path, map_location=device))
        self.actor_target.load_state_dict(self.actor.state_dict())
        self.critic_target.load_state_dict(self.critic.state_dict())
    
    # 开启评估模式
    def eval_mode(self):
        self.train_mode=False
        self.actor.eval()
        self.critic.eval()
        self.actor_target.eval()
        self.critic_target.eval()

def evaluate_performance(env, ddpg, num_episodes=10):
    total_rewards = []
    for _ in range(num_episodes):
        state = env.reset()
        episode_reward = 0
        done = False
        while not done:
            action = ddpg.select_action(state)
            next_state, reward, done = env.step(action,0)
            episode_reward += reward
            state = next_state
        total_rewards.append(episode_reward)
    avg_reward = np.mean(total_rewards)
    print(f"Average Evaluation Reward: {avg_reward}")
    return avg_reward

# 训练DDPG模型
def train_ddpg(env,max_episodes, max_timesteps, best_score, path,step_space):
    state_dim = env.state_dim
    action_dim = env.action_dim
    max_action = env.max_action  # 动作的最大幅度
    step_space=step_space  # 动作的最大幅度
    min_action = env.min_action  # 动作的最小幅度

    ddpg = DDPG(state_dim, action_dim, max_action)
    # 定义加载模型的路径
    actor_path = path + "best_actor.pth"
    critic_path = path + "best_critic.pth"

    # 检查是否存在已训练好的模型
    if os.path.exists(actor_path) and os.path.exists(critic_path):
        print("Loading existing models...")
        ddpg.load(actor_path, critic_path)
        print("Models loaded successfully.")

    replay_buffer = ReplayBuffer()
    all_score = []
    for episode in (to:= tqdm(range(max_episodes), colour='red')):
        state = env.reset()
        done = False
        t = 0
        episode_reward = 0.0  # 初始化episode奖励
        while not done and t <= max_timesteps:
            t += 1
            action = ddpg.select_action(state)

            # 增加探索
            if np.random.rand() < 0.1:  # 10%的概率随机探索
                action = np.random.uniform(-1, 1, size=env.action_dim)
            else:
                # 增加噪声
                noise = np.random.normal(0, 0.1, size=env.action_dim)
                action = np.clip(action + noise, -1, 1)

            next_state, reward, done = env.step(action,t)            
            replay_buffer.add(state, action, reward, next_state, done)
            state = next_state
            episode_reward += reward  # 累加奖励
            
            if t == max_timesteps:
                print(f"Episode finished after {t} timesteps")
                print("+" * 50)
            if replay_buffer.size() > batch_size:
                ddpg.train(replay_buffer, batch_size)
        
        # if env.best_reward:
        #     torch.save(ddpg.actor.state_dict(), path + f"/{episode}_best_reward.pth")

        to.set_description(f"Episode {episode}, Average Reward: {np.mean(episode_reward)}")  # 更新进度条描述
        if episode % 50 == 0:
            print(f"================================================Episode finished after {t} timesteps")
            print(f"Episode {episode}, Reward: {episode_reward}")  # 打印总奖励

        # 每100个episode评估一次性能
        # if episode % 100 == 0:
        #     avg_reward = evaluate_performance(env, ddpg)
        #     print(f"Episode {episode}, Average Evaluation Reward: {avg_reward}")

        all_score.append(np.mean(episode_reward))
        with open(os.path.join(path, 'all_scores.txt'), 'a') as f:
             f.write(f'{episode_reward}\n')
        avg_score = np.mean(all_score[-20:])
        if avg_score > best_score:
            best_score = avg_score
            print("=" * 50)
            print(f"Episode finished after {t} timesteps")
            print(f"Solved! Average reward: {avg_score:.2f}")
            print(f"Episode {episode + 1}, Action: {action}")

            # 保存当前的最佳模型
            torch.save(ddpg.actor.state_dict(), path + "/best_actor.pth")
            torch.save(ddpg.critic.state_dict(), path + "/best_critic.pth")
            print("Model saved!")

            print("=" * 50)
            env.render(episode) if episode != 0 else None

    # 保存最终模型
    torch.save(ddpg.actor.state_dict(), os.path.join(path, "final_actor.pth"))
    torch.save(ddpg.critic.state_dict(), os.path.join(path, "final_critic.pth"))
    print("Final model saved!")

# 验证模型的性能
def validate_ddpg(env, max_timesteps, path,step_space,eval_episodes):
    state_dim = env.state_dim
    action_dim = env.action_dim
    max_action = env.max_action  # 动作的最大幅度
    step_space = step_space  # 动作步长

    ddpg = DDPG(state_dim, action_dim, max_action)

    # 加载已训练的模型
    actor_path = os.path.join(path, "best_actor.pth")
    critic_path = os.path.join(path, "best_critic.pth")
    ddpg.load(actor_path, critic_path)
    # 开始评估模式
    ddpg.eval_mode()

    for episode in range(eval_episodes):
        print(f"=======================================Episode {episode}")

        state = env.reset()
        done = False
        t = 0
        episode_reward = 0.0  # 初始化episode奖励
        while not done and t <= max_timesteps:
            print(f"Episode {t},state:{state[:2]}")
            t += 1
            action = ddpg.select_action(state)
            print(f"Episode {t},action:{action}")
            next_state, reward, done = env.step(action, t)
            state = next_state
            episode_reward += reward  # 累加奖励

            if done:
                #计算距离
                min_length=np.linalg.norm(env.mean_data-state[:2], axis=-1)
                print(f"min_length:{min_length}")

                print(f"Validation finished after {t} timesteps with reward {episode_reward}")
                break

        # 渲染最终结果
        env.show_picture(episode)
        print(f"Final reward: {episode_reward}")
    return episode_reward

def find_valid_path(dirs, folder_name):
    for idx, path in enumerate(dirs):
        parts = path.split("/")
        drive = parts[0] + "/"  # 盘符，例如 'D:'
        if os.path.exists(drive):  # 检查盘符是否存在
            current_path = drive
            for folder in parts[1:]:  # 依次检查文件夹
                current_path = os.path.join(current_path, folder)
                if os.path.exists(current_path):  # 如果文件夹不存在，跳出当前路径检查
                    return dirs[idx] + folder_name

def remove_file(path):
    # 判断路径是否存在
    if os.path.exists(path):
        # 遍历路径下的所有文件和文件夹
        for filename in os.listdir(path):
            # 构建完整的文件或文件夹路径
            file_path = os.path.join(path, filename)
            # 如果是文件，则删除文件
            if os.path.isfile(file_path):
                os.remove(file_path)  # 删除文件
            # 如果是文件夹，则递归删除文件夹及其内容
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)  # 递归删除文件夹及其内容


#创建文件夹
def create_path():
    folder_name = datetime.datetime.now().strftime("%Y-%m-%d/")
    dirs = [
        "D:/Desktop/临时文件夹/5/",
        "F:/实验数据/Month_7/",
        "F:/cluo/MARL/",
        "/home/<USER>/data/",
        "/content/work/"
    ]

    data_dir = find_valid_path(dirs, folder_name)
    print("当前文件夹路径：", data_dir)
    # 如果文件夹不存在则创建
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    
    return data_dir


def copy_file(target_directory):        
    current_file_path = os.path.abspath(__file__)  # 使用os.path.abspath获取当前文件的绝对路径        
    target_file_path = os.path.join(target_directory, os.path.basename(current_file_path))  # 组合目标路径和文件名        
    shutil.copy2(current_file_path, target_file_path)  # 复制文件并保留元数据       
    return target_file_path


if __name__ == "__main__":
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    data_dir=create_path()

    area_side_length = 100
    np.random.seed(0)
    data=(np.random.rand(100, 2) *area_side_length).astype(np.float64) #地面用户坐标
    users_transmit_power=10 * np.log10(np.random.random(len(data)) * 50 / 1000)# 单位dB

    start_point=np.array([0,0], dtype=np.float64)
    end_point=np.array([100,100], dtype=np.float64)
    max_action=np.max(data,axis=0)
    min_action=np.mean(data,axis=0)

    max_episodes = 10000
    max_timesteps = 300
    batch_size = 128
    best_score = -inf
    step_space=1
    env = ClusteringEnv(data,users_transmit_power,start_point, max_action, min_action, step_space, data_dir)
    train_mode=True
    # train_mode=False
    if train_mode:
        #清空文件夹
        remove_file(data_dir)
        copy_file(data_dir)
        # copy_Envfile(data_dir)
        #模型训练
        train_ddpg(env,max_episodes, max_timesteps, best_score, data_dir,step_space)
    else:
        # 运行验证
        validate_ddpg(env, max_timesteps, data_dir,step_space,10)
